<template>
  <ConfigProvider :locale="getAntdLocale" :theme="{ algorithm: compactAlgorithm }">
    <AppProvider>
      <RouterView />
    </AppProvider>
  </ConfigProvider>
</template>

<script lang="ts" setup>
  import { provide } from 'vue';
  import { ConfigProvider, theme } from 'ant-design-vue';
  import { AppProvider } from '/@/components/Application';
  import { useTitle } from '/@/hooks/web/useTitle';
  import { useLocale } from '/@/locales/useLocale';
  import { useIdcSvr } from '@/hooks/idcard/useIdcSvr';
  // 解决日期时间国际化问题
  import 'dayjs/locale/zh-cn';

  const { send, idcData } = useIdcSvr();
  provide('idCardDataKey', idcData);
  provide('idCardSendMethod', send);

  // support Multi-language
  const { getAntdLocale } = useLocale();
  const { compactAlgorithm } = theme;

  useTitle();
  // 动态主题逻辑已移除，使用默认主题配置
</script>
<style lang="less">
  // update-begin--author:liaozhiyang---date:20230803---for：【QQYUN-5839】windi会影响到html2canvas绘制的图片样式
  img {
    display: inline-block;
  }
  // update-end--author:liaozhiyang---date:20230803---for：【QQYUN-5839】windi会影响到html2canvas绘制的图片样式

  :deep .ant-collapse-content-box {
    padding: 8px 8px;
  }
</style>
