<template>
  <a-modal
    v-bind="$attrs"
    :title="getTitle"
    :open="visible"
    size="small"
    width="80%"
    style="top: 1px"
    :okButtonProps="{ hidden: true }"
    :cancelText="'关闭'"
    :destroy-on-close="true"
    @cancel="
      () => {
        visible = false;
      }
    "
  >
    <div style="height: 90vh; overflow: auto">
      <div id="reportViewer"></div>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { computed, nextTick, ref } from 'vue';

  const visible = ref(false);
  let report = {};
  const filename = ref('');
  

  function open(options) {
    visible.value = true;
    report = options.template;
    filename.value = options.filename;
    openReport(report);
  }

  async function openReport(report) {
    nextTick(() => {
      var viewer = new MESCIUS.ActiveReportsJS.ReportViewer.Viewer('#reportViewer', {
        language: 'zh',
        viewMode: 'Continuous',
        ErrorHandler: (error) => {
          console.error(error.message);
          return true;
        },
      });
      viewer.viewMode = 'Continuous';
      viewer.resetDocument();
      viewer.availableExports = ['pdf'];
      viewer.open(report);
    });
  }

  const getTitle = computed(() => '预览报表-' + filename.value);

  defineExpose({
    open,
  });
</script>
<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 100px);
  }
</style>
<style>
  #reportViewer {
    margin: 0 auto;
    width: 100%;
    height: 100vh;
  }
</style>
