import { defineComponent, getCurrentInstance, h, onMounted, ref } from 'vue';

let jvxeRegistered = false;
let jvxeRegistering = false;
let jvxeWaiters: Array<() => void> = [];

let RealJVxeComponent: any = null;

async function ensureJVxeRegistered(app: any): Promise<void> {
  if (jvxeRegistered) return;
  if (jvxeRegistering) {
    await new Promise<void>((resolve) => jvxeWaiters.push(resolve));
    return;
  }
  jvxeRegistering = true;
  try {
    const [jvxeMod, customMod] = await Promise.all([
      import('/@/components/jeecg/JVxeTable'),
      import('/@/components/JVxeCustom'),
    ]);
    jvxeMod.registerJVxeTable(app);
    await customMod.registerJVxeCustom();
    RealJVxeComponent = jvxeMod.JVxeTable;
    jvxeRegistered = true;
  } catch (error) {
    console.warn('JVxe 懒注册失败：', error);
  } finally {
    jvxeRegistering = false;
    const resolvers = jvxeWaiters.slice();
    jvxeWaiters.length = 0;
    resolvers.forEach((r) => r());
  }
}

export function createJVxeLazyComponent() {
  return defineComponent({
    name: 'JVxeTable',
    inheritAttrs: true,
    setup(_, { attrs, slots }) {
      const ready = ref(false);
      const Real = ref<any>(null);
      const instance = getCurrentInstance();

      onMounted(async () => {
        const app = instance?.appContext.app;
        if (!app) return;
        await ensureJVxeRegistered(app);
        Real.value = RealJVxeComponent;
        ready.value = !!Real.value;
      });

      return () => {
        if (ready.value) {
          return h(Real.value as any, attrs as any, slots);
        }
        // 轻量占位，避免首屏同步引入 vxe-table
        return h('div', { class: 'jvxe-loading-placeholder' });
      };
    },
  });
}


