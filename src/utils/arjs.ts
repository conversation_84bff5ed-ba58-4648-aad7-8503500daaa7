export async function ensureArjsFonts(): Promise<void> {
  const win: any = window as any;
  const ARJS = win?.MESCIUS?.ActiveReportsJS;
  if (!ARJS) return;

  if (!win.__arjsFontsPromise) {
    // 先探测配置是否存在且为 JSON，避免 404 返回 HTML 造成异常
    const candidateUrls = ['/fonts/fontsConfig.json', '/resource/activeReportJs/fonts/fontsConfig.json'];
    const tryLoad = async (url: string) => {
      const res = await fetch(url, { method: 'GET' });
      if (!res.ok) return false;
      try {
        await res.clone().json();
      } catch (_) {
        return false;
      }
      await ARJS.Core.FontStore.registerFonts(url).catch(() => Promise.resolve());
      return true;
    };
    win.__arjsFontsPromise = (async () => {
      for (const url of candidateUrls) {
        const ok = await tryLoad(url);
        if (ok) return;
      }
    })()
      .then(async (res) => {
        return Promise.resolve();
      })
      .catch(() => Promise.resolve());
  }
  await win.__arjsFontsPromise;
}


