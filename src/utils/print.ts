// 使用全局 ActiveReportsJS，移除 ESM 依赖
import { ensureArjsFonts } from '@/utils/arjs';
export enum PrinterType {
  Apply = 'apply',
  Barcode = 'barcode',
  Guide = 'guide',
}
export function printReportDirect(template: Object, type: string = 'guide'): Promise<void> {
  return new Promise((resolve, reject) => {
    const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
    if (!ARJS) {
      reject(new Error('ActiveReportsJS 未加载'));
      return;
    }
    ensureArjsFonts().then(() => {
      const PDF = ARJS.PdfExport;
      const settings = {
        info: {
          title: type == 'guide' ? '导引单' : '申请单',
          author: '无',
        },
        pdfVersion: '1.7',
      };

      const pageReport = new ARJS.Core.PageReport();
      pageReport
        .load(template)
        .then(() => {
          return pageReport.run();
        })
        .then((pageDocument) => {
          return PDF.exportDocument(pageDocument, settings);
        })
        .then((result) => {
          blobToDataURI(result.data, (r) => {
            printPDF(r, type);
            resolve();
          });
        })
        .catch(reject);
    });
  });
}

function getPrinterName(type) {
  //从localStorage中获取打印机名称
  const printersStr = localStorage.getItem('printers');
  if (printersStr) {
    const printers = JSON.parse(printersStr);
    return printers[type].name;
  }
  return '';
}

export function printPDF(strURLorContent, type = 'apply') {
  try {
    const LODOP = getLodop();
    LODOP.PRINT_INITA('');
    const printerName = getPrinterName(type);

    if (printerName) {
      LODOP.SET_PRINTER_INDEX(printerName);
    } else {
      LODOP.SET_PRINTER_INDEX(-1);
    }
    LODOP.ADD_PRINT_PDF(0, 0, '100%', '100%', strURLorContent);
    LODOP.PRINT(); //直接打印
    //LODOP.PRINT_DESIGN() // 设计模式
    //LODOP.PREVIEW(); // 打印预览
  } catch (err) {
    // alert("请安装C-LODOP打印插件后再打印")
  }
}

export async function printPDFBlob(blobData, type = 'apply') {
  blobToDataURI(blobData, (r) => {
    printPDF(r, type);
  });
}

/*export async function printPDFBlob(blobData: Blob, type: string = 'apply'): Promise<void> {
  try {
    const dataURI = await blobToDataURI(blobData);
    printPDF(dataURI, type);
  } catch (error) {
    console.error('Error printing PDF blob:', error);
  }
}*/

export async function printRemoteFile(url: string, type: string = 'apply'): Promise<void> {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download file: ${response.statusText}`);
    }
    const blob = await response.blob();
    await printPDFBlob(blob, type);
  } catch (error) {
    console.error('Error printing remote file:', error);
  }
}

//数据流转成pdf
/*export function blobToDataURI(blob, callback) {
  const reader = new FileReader();
  reader.readAsDataURL(blob);
  reader.onload = function (e) {
    callback(e.target.result.replace('data:application/pdf;base64,', ''));
  };
}*/

export function blobToDataURI(blob: Blob, callback: (dataURI: string) => void) {
  const reader = new FileReader();
  reader.readAsDataURL(blob);
  reader.onload = function (e) {
    if (e.target && e.target.result) {
      callback((e.target.result as string).replace('data:application/pdf;base64,', ''));
    }
  };
}

/*export function blobToDataURI(blob: Blob): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(blob);
    reader.onload = function (e) {
      if (e.target && e.target.result) {
        resolve((e.target.result as string).replace('data:application/pdf;base64,', ''));
      } else {
        reject(new Error('Failed to convert blob to data URI'));
      }
    };
    reader.onerror = function () {
      reject(new Error('Error reading blob'));
    };
  });
}*/
