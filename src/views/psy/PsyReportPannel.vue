<template>
  <a-card>
    <template #title>
      <a-space>
        <a-typography-title :level="5">{{ title }}</a-typography-title>
        <a-select
            @change="previewReportByTemplate"
            placeholder="请选择报告模版"
            v-model:value="selectedTemplateId"
            size="small"
            style="width: 150px; margin-left: 20px"
            :options="reportTemplateList"
        />
      </a-space>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="printReport" :disabled="!personalQuest?.id">打印</a-button>
      </a-space>
    </template>
    <a-spin :spinning="summaryAdviceLoading">
      <div class="viewer-host">
        <div ref="viewerEl"></div>
      </div>
    </a-spin>
  </a-card>
</template>

<script lang="ts" setup>
import { computed, defineExpose, onMounted, ref, nextTick } from 'vue';
import { message, SelectProps } from 'ant-design-vue';
import { getTemplateById, listByType } from '@/views/basicinfo/Template.api';
import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
import { ensureArjsFonts } from '@/utils/arjs';
import {getPsyReportData} from "@/views/psy/PsyCustomer.api";

const title = ref<string>('');
const visible = ref<boolean>(false);
const viewerEl = ref<HTMLElement | null>(null);
const personalQuest = ref<any>({});
const selectedTemplateId = ref(null);
const btnLoading = ref<boolean>(false);
const matchedTemplateId = computed(() => {
  let quest = personalQuest.value;
  if (!quest.id) {
    return null;
  }
  let module = quest.module;
  let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == module);
  return regTemplateList[0]?.id || null;
});

const summaryAdviceLoading = ref<boolean>(false);
const generatePdfBackendsFlag = ref<string>('');

let currentReport = {};
let preloadPdfData: Blob | null = null;

let viewer: any;
onMounted(async () => {
  await nextTick();
  const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
  if (!ARJS || !viewerEl.value) return;
  viewer = new ARJS.ReportViewer.Viewer(viewerEl.value, {
    language: 'zh',
    viewMode: 'Continuous',
    ErrorHandler: (error) => {
      console.error(error?.message || error);
      return true;
    },
  });
  viewer.viewMode = 'Continuous';
});
const pageReport = (window as any).MESCIUS?.ActiveReportsJS?.Core
  ? new (window as any).MESCIUS.ActiveReportsJS.Core.PageReport()
  : null;
const PDF = (window as any).MESCIUS?.ActiveReportsJS?.PdfExport;

function exportPdf(report) {
  return new Promise((resolve, reject) => {
    const settings = {
      info: {
        title: `${personalQuest.value.personalName}-${personalQuest.value.name}心理测评报告`,
        author: '',
      },
      pdfVersion: '1.7',
    };
    const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
    if (!ARJS || !pageReport || !PDF) {
      reject(new Error('ActiveReportsJS 未加载'));
      return;
    }
    ensureArjsFonts()
      .then(() => pageReport.load(report))
      .then(() => pageReport.run())
      .then((pageDocument) => PDF.exportDocument(pageDocument, settings))
      .then((result) => resolve(result.data))
      .catch((error) => reject(error));
  });
}

/**报告预览打印*/
const reportTemplateList = ref<SelectProps['options']>([]);
const originReportTemplateList = ref([]);

const emit = defineEmits(['register', 'success']);

async function open(quest) {
  console.log(quest)
  visible.value = true;
  personalQuest.value = quest;
  title.value = `心理测评报告-${quest.personalName}`;
  if (matchedTemplateId.value) {
    selectedTemplateId.value = matchedTemplateId.value;
    previewReport(matchedTemplateId.value);
  } else {
    message.warn('未找到对应的报告模板！');
  }
}

async function previewReport(templateId) {
  if (!personalQuest.value.id) {
    message.warn('请选择问卷记录！');
    return;
  }
  if (!matchedTemplateId.value) {
    message.warn('请选择报告模板！');
    return;
  }
  //获取报告模版内容
  try {
    summaryAdviceLoading.value = true;
    const templateRes = await getTemplateById({ id: templateId });
    let template = JSON.parse(templateRes.content);

    const reportDataRes = await getPsyReportData({ personalQuestionnaireId: personalQuest.value.id });
    summaryAdviceLoading.value = false;
    if (reportDataRes.success) {
      let reportData = reportDataRes.result;

      //需要处理报告中的图片
      //console.log(reportData);
   /*   if (reportData.reportImgList?.length > 0) {
        reportData.reportImgList?.forEach((item) => {
          item.text = getFileAccessHttpUrl(item.text);
        });
      }*/
      //console.log(reportData.groupByFunctionMap?.lab_exam);

  /*    if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
        reportData.groupByFunctionMap.lab_exam.forEach((group) => {
          if (group.reportPicBeanList?.length > 0) {
            group.reportPicBeanList.forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          }
        });
      }*/

    /*  if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
        reportData.groupByFunctionMap.lab_exam.forEach((group) => {
          if (group.reportPicBeanList?.length > 0) {
            group.reportPicBeanList.forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          }
        });
      }*/

     /* if (reportData.groupByFunctionPicMap) {
        Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
          reportData.groupByFunctionPicMap[key].forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        });
      }*/

      //console.log(reportData.groupByFunctionMap.lab_exam);
    /*  if (reportData.summaryAdvice?.auditorSignPic) {
        reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
      }
      if (reportData.summaryAdvice?.preAuditorSignPic) {
        reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
      }
      if (reportData.summaryAdvice?.creatorSignPic) {
        reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
      }*/
      //console.log('=======reportData==========', reportData);
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
      currentReport = template;
      //开启异步预载pdfData

      /* preloading.value = true;
      exportPdf(template).then((pdfData) => {
        preloadPdfData = pdfData;
        preloading.value = false;
        //message.warn('报告预载成功！');
      });*/

      if (viewer) {
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(template);
        viewer.viewMode = 'Continuous';
      }
    } else {
      message.error('获取报告数据失败');
    }
  } catch (error) {
    console.error(error);
  }
}

function printReport() {
  if (!currentReport) {
    message.warn('请先预览报告！');
    return;
  }
  if (viewer) viewer.print();

}




function fetchReportTemplateList() {
  listByType({ type: '报告', regType: '个人' }).then((res) => {
    if (res.success) {
      originReportTemplateList.value = res.result;
      reportTemplateList.value = res.result.map((item) => {
        return {
          value: item.id,
          label: item.name,
        };
      });
    }
  });
}

function fetchGeneratePdfBackendsFlag() {
  querySysParamByCode({ code: 'generatePdfBackends' }).then((res) => {
    generatePdfBackendsFlag.value = res.result;
  });
}

onMounted(() => {
  fetchReportTemplateList();
  fetchGeneratePdfBackendsFlag();
});

function previewReportByTemplate() {
  previewReport(selectedTemplateId.value);
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.viewer-host {
  width: 100%;
  height: calc(100vh - 200px);
}
</style>
<style>
#reportViewer {
  margin: 0 auto;
  width: 100%;
  height: 100vh;
}
</style>
