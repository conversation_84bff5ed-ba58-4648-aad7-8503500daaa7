<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item name="companyId">
              <template #label><span title="所在单位">所在单位</span></template>
              <j-async-search-select placeholder="请选择所在单位" v-model:value="queryParam.companyId" dict="company where del_flag=0,name,id" />
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item name="regName">
              <template #label><span title="预约名称">预约名称</span></template>
              <a-input placeholder="请输入预约名称" v-model:value="queryParam.regName" />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="examType">
                <template #label><span title="体检类别">体检类别</span></template>
                <j-dict-select-tag placeholder="请选择体检类别" v-model:value="queryParam.examType" dict-code="examination_type" />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item>
                <template #label><span title="开始日期">开始日期</span></template>
                <div style="display: flex">
                  <a-form-item name="startCheckDate_begin" style="margin-bottom: 0">
                    <a-date-picker
                      value-format="YYYY-MM-DD"
                      placeholder="请选择开始日期"
                      v-model:value="queryParam.startCheckDate_begin"
                      class="query-group-cust"
                    />
                  </a-form-item>
                  <span class="query-group-split-cust">~</span>
                  <a-form-item name="startCheckDate_end" style="margin-bottom: 0">
                    <a-date-picker
                      value-format="YYYY-MM-DD"
                      placeholder="请选择结束日期"
                      v-model:value="queryParam.startCheckDate_end"
                      class="query-group-cust"
                    />
                  </a-form-item>
                </div>
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="serviceManager">
                <template #label><span title="客服专员">客服专员</span></template>
                <j-select-user-by-dept
                  placeholder="请选择客服专员"
                  v-model:value="queryParam.serviceManager"
                  @change="(value) => handleFormJoinChange('serviceManager', value)"
                />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="checkoutStatus">
                <template #label><span title="封账状态">封账状态</span></template>
                <j-switch placeholder="请选择封账状态" v-model:value="queryParam.checkoutStatus" :options="[1, 0]" query />
              </a-form-item>
            </a-col>
            <a-col :lg="6">
              <a-form-item name="lockStatus">
                <template #label><span title="锁定状态">锁定状态</span></template>
                <j-switch placeholder="请选择锁定状态" v-model:value="queryParam.lockStatus" :options="[1, 0]" query />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-select
          placeholder="请选择报告模版"
          v-model:value="currentReportTemplateId"
          size="middle"
          style="width: 150px"
          :options="reportTemplateList"
        />
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CompanyRegModal ref="registerModal" @success="handleSuccess" />
    <report-vue ref="reportRef" />
  </div>
</template>

<script lang="ts" name="CompanyRegList" setup>
  import { onMounted, provide, reactive, ref } from 'vue';
  import { companyRegIdKey, companyRegKey } from '/@/providekey/provideKeys';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from '@/views/reg/CompanyReg.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from '@/views/reg/CompanyReg.api';
  import CompanyRegModal from '@/views/reg/components/CompanyRegModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JSelectUserByDept from '/@/components/Form/src/jeecg/components/JSelectUserByDept.vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import { message, SelectProps } from 'ant-design-vue';
  import { getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { getTeamReport } from '@/views/summary/Summary.api';
  import { ICompanyReg } from '#/types';
  import Report from '@/components/Report/Report.vue';

  const companyRegId = reactive({ value: '', setValue: (val) => (companyRegId.value = val) });
  const companyReg = reactive({ value: {}, setValue: (val) => (companyReg.value = val) });
  provide(companyRegIdKey, companyRegId);
  provide(companyRegKey, companyReg);

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '单位预约',
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      canColDrag: true,
      size: 'small',
      clickToRowSelect: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: '单位预约',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    companyRegId.setValue('');
    companyReg.setValue({});
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }

  /**
   * 编辑事件
   */
  function handleReport(record: Recordable) {
    /*companyRegId.setValue(record.id);
    companyReg.setValue(record);
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);*/
    previewReport(record as ICompanyReg);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    companyRegId.setValue(record.id);
    companyReg.setValue(record);
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '团体报告',
        onClick: handleReport.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  /**
   * form点击事件(以逗号分割)
   * @param key
   * @param value
   */
  function handleFormJoinChange(key, value) {
    queryParam[key] = value.join(',');
  }

  /**报告预览打印*/
  const currentReg = ref(null);
  const reportRef = ref(null);
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const currentReportTemplateId = ref(null);
  const reportDataLoading = ref(false);

  async function previewReport(companyReg: ICompanyReg) {
    if (!companyReg.id) {
      message.warn('请选择体检单位');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    //获取报告模版内容
    reportDataLoading.value = true;
    try {
      const templateRes = await getTemplateById({ id: currentReportTemplateId.value });
      let template = JSON.parse(templateRes.content);
      const reportDataRes = await getTeamReport({ companyRegId: companyReg.id });
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;
        //需要处理报告中的图片
        //console.log(reportData);
        /*if (reportData.reportImgList?.length > 0) {
          reportData.reportImgList?.forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        }
        console.log(reportData.groupByFunctionMap?.lab_exam);

        if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }
        console.log(reportData.groupByFunctionMap.lab_exam);
        if (reportData.summaryAdvice?.auditorSignPic) {
          reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
        }
        if (reportData.summaryAdvice?.preAuditorSignPic) {
          reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
        }
        if (reportData.summaryAdvice?.creatorSignPic) {
          reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
        }*/
        //console.log(reportData);
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
        if (!reportRef.value) {
          message.error('报告组件初始化失败！');
          return;
        }
        reportRef.value.open({
          filename: `${companyReg.regName}的体检报告`,
          template: template,
        });
      } else {
        message.error('获取报告数据失败');
      }
    } catch (error) {
      console.error(error);
    } finally {
      reportDataLoading.value = false;
    }
  }

  async function printReport(companyReg: ICompanyReg) {
    if (!companyReg.id) {
      message.warn('请选择体检单位');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板');
      return;
    }
    /*if (companyReg.status != '审核通过') {
      message.warn('总检未审核，不可以打印报告！');
      return;
    }*/

    previewReport(companyReg);

    //await updateReportPrintTimes({ id: customerSummary.value.id });

    //获取报告模版内容
    /*reportDataLoading.value = true;
    let templateRes = await getTemplateById({ id: currentReportTemplateId.value });
    let template = JSON.parse(templateRes.content);
    let reportDataRes = await getReportData({ customerRegId: currentReg.value.id });
    if (!reportDataRes.success) {
      message.error('获取报告数据失败');
      reportDataLoading.value = false;
      return;
    }
    let reportData = reportDataRes.result;
    if (reportData.reportImgList?.length > 0) {
      reportData.reportImgList?.forEach((item) => {
        item.text = getFileAccessHttpUrl(item.text);
      });
    }
    if (reportData.summaryAdvice?.auditorSignPic) {
      reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
    }
    if (reportData.summaryAdvice?.preAuditorSignPic) {
      reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
    }
    if (reportData.summaryAdvice?.creatorSignPic) {
      reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
    }

    template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
    await printReportDirect(template, PrinterType.Guide);
    await updateReportPrintTimes({ regId: currentReg.value.id });
    reportDataLoading.value = false;*/
  }

  function fetchReportTemplateList() {
    getReportTemplate({ type: '报告', regType: '团体' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
        reportTemplateList.value = res.result.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  }
  function filterTemplateByReg() {
    let reg = currentReg.value;
    if (!reg.id) {
      return;
    }
    let examCategory = reg.examCategory;
    let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == examCategory);
    currentReportTemplateId.value = regTemplateList[0]?.id;
  }

  onMounted(() => {
    fetchReportTemplateList();
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
