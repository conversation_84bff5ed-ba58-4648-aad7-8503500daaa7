<template>
  <div>
    <SignBoard ref="signRef" @sign-done="onSignDone" @connected="setSignboardStatus" />

    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="[4, 4]">
          <a-col :span="12">
            <a-input allow-clear size="middle" placeholder="请输入体检号" v-model:value="queryParam.examNo" />
          </a-col>
          <a-col :span="12">
            <a-input allow-clear size="middle" placeholder="请输入姓名" v-model:value="queryParam.name" />
          </a-col>
          <a-col :span="12">
            <j-dict-select-tag
              dict-code="e_report_status"
              size="middle"
              placeholder="电子报告状态"
              v-model:value="queryParam.eReportStatus"
              style="width: 100%"
            />
          </a-col>
          <a-col :span="12">
            <j-dict-select-tag
              dict-code="paper_report_status"
              size="middle"
              placeholder="纸质报告状态"
              v-model:value="queryParam.paperReportStatus"
              style="width: 100%"
            />
          </a-col>

          <template v-if="toggleSearchStatus">
            <a-col :lg="12">
              <j-dict-select-tag
                dict-code="examination_type"
                size="middle"
                placeholder="体检类型"
                v-model:value="queryParam.examCatory"
                style="width: 100%"
              />
            </a-col>

            <a-col :span="12">
              <j-async-search-select
                placeholder="请选择医生"
                dict="sys_user where del_flag=0,realname,username"
                v-model:value="queryParam.doctor"
                :allow-clear="true"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="12">
              <a-select v-model:value="queryParam.doctorType" placeholder="医生类型" style="width: 100%">
                <a-select-option value="">无</a-select-option>
                <a-select-option value="初检">初检</a-select-option>
                <a-select-option value="主检">主检</a-select-option>
                <a-select-option value="审核">审核</a-select-option>
              </a-select>
            </a-col>

            <a-col :span="12">
              <a-select v-model:value="queryParam.dateType" placeholder="日期类型" style="width: 100%">
                <a-select-option value="登记日期">登记日期</a-select-option>
                <a-select-option value="初检日期">初检日期</a-select-option>
                <a-select-option value="主检日期">主检日期</a-select-option>
                <a-select-option value="审核日期">审核日期</a-select-option>
                <a-select-option value="打印日期">打印日期</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="12">
              <a-select v-model:value="queryParam.sortOrder" placeholder="择排序方式" style="width: 100%">
                <template #suffixIcon><SortAscendingOutlined /> </template>
                <a-select-option value="降序">降序</a-select-option>
                <a-select-option value="升序">升序</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="24">
              <a-range-picker
                v-model:value="regDateRange"
                placement="日期范围"
                @change="searchQuery"
                :presets="rangePresets"
                :allow-clear="false"
                style="width: 100%"
              />
            </a-col>
            <a-col :span="12">
              <j-async-search-select
                size="middle"
                placeholder="所属预约"
                @change="getTeamList"
                v-model:value="queryParam.companyRegId"
                dict="company_reg,reg_name,id"
                :allow-clear="true"
                style="width: 100%"
              />
            </a-col>
          </template>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button size="middle" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button size="middle" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px"
                >重置</a-button
              >
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <a-space
      ><a-button type="primary" @click="handleBatchReview">批量审阅通过</a-button>
      <a-button type="primary" @click="handleBatchPrint">批量打印</a-button>
      <a-button type="primary" @click="handleBatchTakeover">批量取走</a-button>
      <a-button type="primary" @click="handleBatchResetState">重置状态</a-button></a-space
    >
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle> </template>
      <!--插槽:table标题-->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex == 'ereportStatus'">
          <a-tag :bordered="false" color="orange" v-if="text == '待生成'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="cyan" v-if="text == '待审阅'"> {{ text }} </a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '待发送'"> {{ text }} </a-tag>
          <a-tag :bordered="false" color="green" v-if="text == '已发送'"> {{ text }} </a-tag>
          <a-tag :bordered="false" color="red" v-if="text == '已召回'"> {{ text }} </a-tag>
        </template>
        <template v-if="column.dataIndex == 'paperReportStatus'">
          <a-tag :bordered="false" color="gray" v-if="text == '待打印'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '已打印'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="green" v-if="text == '已取走'"> {{ text }}</a-tag>
        </template>

        <template v-if="column.dataIndex == 'initailSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == 'AI'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'preSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == '自动'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'summaryStatus'">
          <a-tag :bordered="false" :color="record.summaryStatusColor"> {{ record.summaryStatus }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex == 'checkStatus'">
          <a-space>
            <template v-for="status in record.statusStatList">
              <a-popover :title="status.status + ':' + status.count + '项'" trigger="click">
                <template #content>
                  <div style="width: 20vw">
                    <a-table
                      :rowKey="(_, index) => index"
                      :dataSource="status.items"
                      :showHeader="false"
                      size="small"
                      :pagination="false"
                      :scroll="{ y: 200 }"
                    >
                      <a-table-column key="item">
                        <!-- Use the default slot to render each string -->
                        <template #default="{ record, index }"> {{ index + 1 }}、 {{ record }} </template>
                      </a-table-column>
                    </a-table>
                  </div>
                </template>
                <a-tag :bordered="false" :color="status.color" style="cursor: pointer">{{ status.status }}({{ status.count }}项) </a-tag>
              </a-popover>
            </template>
          </a-space>
        </template>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <a @click="handleEdit(record)">审阅</a>
        <a-divider type="vertical" />
        <a-dropdown>
          <a-button type="link" size="small"> 更多 </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="0" @click.stop="verifyReport(record)">审阅通过</a-menu-item>
              <a-menu-item key="2" @click.stop="handlePrint(record)">打 印</a-menu-item>
              <a-menu-item key="3" @click.stop="handleToked(record)">标记取走</a-menu-item>
              <a-menu-item key="6" @click.stop="reGeneratePdf(record)">生成PDF报告</a-menu-item>
              <a-menu-item key="1" @click.stop="showPdfModal(record)">查看PDF报告</a-menu-item>
              <a-menu-item key="7" @click.stop="sendPdf(record)">发送报告通知</a-menu-item>
              <a-menu-item key="4" @click.stop="handleActionRecord(record)">操作记录</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </BasicTable>

    <pdf-modal ref="pdfModalRef" />
    <a-modal v-model:open="isPrinting" title="执行进度" @cancel="closeProgressModal" :footer="null" :closable="false" :maskClosable="false">
      <div style="padding: 20px; justify-content: center; align-items: center; min-height: 100px">
        <a-typography-text type="secondary">{{ currentPrintTip }}</a-typography-text>
        <a-progress :percent="printProgress" v-if="printProgress < 100" />
        <a-result status="success" title="执行完成!" v-else />
      </div>
    </a-modal>
    <ReportActionRecordListModal ref="actionRecordList" />
  </div>
</template>

<script lang="ts" name="customer-reg-list4-print" setup>
  import { onMounted, reactive, ref, watch } from 'vue';
  import type { RangeValue } from '#/types';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns4Report } from '/@/views/reg/CustomerReg.data';
  import { getReportData, listReg } from '/@/views/summary/CustomerRegSummary.api.ts';
  import { companyTeamList } from '/@/views/reg/CompanyReg.api';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { JAsyncSearchSelect } from '@/components/Form';
  import dayjs from 'dayjs';
  import { SortAscendingOutlined } from '@ant-design/icons-vue';
  import { getRegById } from '@/views/summary/Summary.api';
  import PdfModal from '@/components/Report/PdfModal.vue';
  import { getTemplateById, listByType } from '@/views/basicinfo/Template.api';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  // 使用全局 ActiveReportsJS
  import { printPDFBlob, printRemoteFile } from '@/utils/print';
  import {
    markAsTakenBatch,
    markReportAsTaken,
    regenratePdf,
    resetState4ReGeneratePdf,
    sendReportNotify,
    updateReportPrintStatusByIds,
    verifyBatch,
  } from '@/views/reg/CustomerReg.api';
  import ReportActionRecordListModal from '@/views/summary/ReportActionRecordListModal.vue';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import SignBoard from '@/components/SignBoard/SignBoard.vue';

  const { createConfirm, notification } = useMessage();
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'reloadRecord']);
  const formRef = ref();
  const queryParam = reactive<any>({ status: '已登记', summaryStatus: '审核通过', sortOrder: '降序', dateType: '登记日期' });
  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();

  const regDateRange = ref<RangeValue>();
  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs()] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '不限', value: null },
  ]);
  /**表格相关操作*/
  const currentRow = ref<any>({});
  const loading = ref(false);
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      loading,
      showTableSetting: false,
      showIndexColumn: true,
      api: listReg,
      columns: columns4Report,
      canResize: true,
      canColDrag: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      striped: true,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      pagination: {
        pageSize: 15,
      },
      customRow: (record) => {
        return {
          onDblclick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
      afterFetch: (dataSource) => {
        if (currentRow.value && dataSource.length > 0) {
          let record = dataSource.find((item) => item.id === currentRow.value.id);
          if (record) {
            currentRow.value = record;
            emit('reloadRecord', record);
          }
        }
        return dataSource;
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
  });
  const [
    registerTable,
    { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource },
    { rowSelection, selectedRowKeys, selectedRows },
  ] = tableContext;

  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    span: 7,
  });
  const wrapperCol = reactive({
    span: 17,
  });

  /**签字板相关*/
  const currentSignRow = ref<any>({});
  const currentSignRows = ref<any[]>([]);
  const signRef = ref<InstanceType<typeof SignBoard>>();
  const signBoardConntect = ref(false);
  const setSignboardStatus = () => {
    signBoardConntect.value = true;
  };

  function onSignDone(base64: string) {
    console.log('父组件拿到签名结果:', base64);
    if (base64 == null || base64.img == null) {
      message.error('签名失败');
      return;
    }
    if (currentSignRow.value.id) {
      markReportAsTaken({ id: currentSignRow.value.id, signBase64: base64.img }).then((res) => {
        if (res.success) {
          message.success('操作成功！');
          reload();
          currentSignRow.value = {};
        } else {
          message.error('操作失败！');
        }
      });
    } else if (currentSignRows.value.length > 0) {
      markAsTakenBatch({ ids: currentSignRows.value.map((record) => record.id), signBase64: base64.img })
        .then((res) => {
          if (res.success) {
            message.success('操作成功！');
            reload();
            selectedRows.value = [];
            selectedRowKeys.value = [];
            currentSignRows.value = [];
          } else {
            message.error('操作失败！');
          }
        })
        .finally(() => {
          loading.value = false;
        });
    }
  }

  /**批量操作*/
  const generatePdfBackendsFlag = ref<string>('');
  const originReportTemplateList = ref([]);

  const isPrinting = ref(false);
  const printProgress = ref(0);
  const currentPrintTip = ref('');
  const actionRecordList = ref();
  function showProgressModal() {
    isPrinting.value = true;
    printProgress.value = 0; // Reset progress
  }

  function closeProgressModal() {
    printProgress.value = 0;
    isPrinting.value = false; // Close the modal
  }

  function verifyReport(customerReg) {
    loading.value = true;
    verifyBatch({ ids: [{ id: customerReg.id }] })
      .then((res) => {
        if (res.success) {
          message.success('操作成功！');
          reload();
        } else {
          message.error('操作失败！');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  async function getReport(templateId, customerReg) {
    return new Promise(async (resolve, reject) => {
      try {
        // Get report template content
        const templateRes = await getTemplateById({ id: templateId });
        let template = JSON.parse(templateRes.content);

        const reportDataRes = await getReportData({ customerRegId: customerReg.id });

        if (reportDataRes.success) {
          let reportData = reportDataRes.result;

          // Process images in the report
          if (reportData.reportImgList?.length > 0) {
            reportData.reportImgList.forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          }

          if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
            reportData.groupByFunctionMap.lab_exam.forEach((group) => {
              if (group.reportPicBeanList?.length > 0) {
                group.reportPicBeanList.forEach((item) => {
                  item.text = getFileAccessHttpUrl(item.text);
                });
              }
            });
          }

          if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
            reportData.groupByFunctionMap.lab_exam.forEach((group) => {
              if (group.reportPicBeanList?.length > 0) {
                group.reportPicBeanList.forEach((item) => {
                  item.text = getFileAccessHttpUrl(item.text);
                });
              }
            });
          }

          if (reportData.groupByFunctionPicMap) {
            Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
              reportData.groupByFunctionPicMap[key].forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            });
          }

          if (reportData.summaryAdvice?.auditorSignPic) {
            reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
          }
          if (reportData.summaryAdvice?.preAuditorSignPic) {
            reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
          }
          if (reportData.summaryAdvice?.creatorSignPic) {
            reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
          }

          template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
          resolve(template);
        } else {
          message.error('获取报告数据失败');
          reject(new Error('Failed to get report data'));
        }
      } catch (error) {
        console.error(error);
        reject(error);
      }
    });
  }

  const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
  const pageReport = ARJS ? new ARJS.Core.PageReport() : null;
  const PDF = ARJS?.PdfExport;
  import { ensureArjsFonts } from '@/utils/arjs';

  function exportPdf(report, customerReg) {
    return new Promise((resolve, reject) => {
      const settings = {
        info: {
          title: `${customerReg.name}-${customerReg.examNo}体检报告`,
          author: '',
        },
        pdfVersion: '1.7',
      };
      const AR = (window as any).MESCIUS?.ActiveReportsJS;
      if (!AR || !pageReport || !PDF) {
        reject(new Error('ActiveReportsJS 未加载'));
        return;
      }
      ensureArjsFonts()
        .then(() => pageReport.load(report))
        .then(() => pageReport.run())
        .then((pageDocument) => PDF.exportDocument(pageDocument, settings))
        .then((result) => resolve(result.data))
        .catch((error) => reject(error));
    });
  }
  // 添加这个辅助函数，用于限制并发数
  async function asyncPool(poolLimit, array, iteratorFn) {
    const ret = []; // 存储所有的异步任务
    const executing = []; // 存储正在执行的异步任务

    for (const item of array) {
      const p = Promise.resolve().then(() => iteratorFn(item));
      ret.push(p);

      if (poolLimit <= array.length) {
        const e = p.then(() => executing.splice(executing.indexOf(e), 1));
        executing.push(e);
        if (executing.length >= poolLimit) {
          await Promise.race(executing);
        }
      }
    }
    return Promise.all(ret);
  }

  async function handleBatchReview() {
    if (selectedRows.value.length == 0) {
      message.error('请先选择一条记录！');
      return;
    }

    //需要时已生成电子报告的记录
    let satisfiedRecords = selectedRows.value.filter((record) => record.ereportStatus == '待审阅');
    let unsatisfiedRecords = selectedRows.value.filter((record) => record.ereportStatus != '待审阅');
    if (satisfiedRecords.length == 0) {
      message.error('没有符合审阅条件的记录！');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `共选择了${selectedRows.value.length}条记录，其中${unsatisfiedRecords.length}条不可审阅，${satisfiedRecords.length}条可审阅，确定要审阅通过吗？`,
      onOk: () => {
        let data = satisfiedRecords.map((record) => {
          return { id: record.id };
        });
        verifyBatch({ ids: data }).then((res) => {
          if (res.success) {
            message.success('操作成功！');
            reload();
            selectedRows.value = [];
            selectedRowKeys.value = [];
          } else {
            message.error('操作失败！');
          }
        });
      },
    });
  }

  async function handleBatchPrint() {
    if (selectedRows.value.length == 0) {
      message.error('请先选择一条记录！');
      return;
    }

    showProgressModal();

    const totalTasks = selectedRows.value.length;
    let completedTasks = 0;

    const concurrencyLimit = 10; // 设置并发限制

    const tasks = selectedRows.value.map((reg, index) => {
      return async () => {
        // 更新当前处理提示
        currentPrintTip.value = `正在处理第 ${completedTasks + 1}/${totalTasks} 条记录`;

        let eReportUrl = getFileAccessHttpUrl(reg.ereportUrl);
        if (!eReportUrl) {
          let report = await getReport(getMatchedTemplateId(reg), reg);
          let pdfData = await exportPdf(report, reg);
          await printPDFBlob(pdfData, 'report');
        } else {
          await printRemoteFile(eReportUrl);
        }

        await updateReportPrintStatusByIds({ id: reg.id });
        completedTasks++;
        printProgress.value = Math.round((completedTasks / totalTasks) * 100);
        currentPrintTip.value = `已完成 ${completedTasks}/${totalTasks} 条记录`;
      };
    });

    try {
      await asyncPool(concurrencyLimit, tasks, (task) => task());
    } catch (error) {
      console.error(error);
    }

    await reload();
    selectedRows.value = [];
    selectedRowKeys.value = [];
    setTimeout(() => {
      closeProgressModal();
    }, 2000);
  }

  async function handleBatchTakeover() {
    if (selectedRows.value.length == 0) {
      message.error('请先选择一条记录！');
      return;
    }

    //需要是已打印的记录
    let satisfiedRecords = selectedRows.value.filter((record) => record.paperReportStatus == '已打印');
    let unsatisfiedRecords = selectedRows.value.filter((record) => record.paperReportStatus != '已打印');
    if (satisfiedRecords.length == 0) {
      message.error('所选记录没有符合标记取走条件的记录！');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `共选择了${selectedRows.value.length}条记录，其中${unsatisfiedRecords.length}条未打印，${satisfiedRecords.length}条已打印，确定要标记取走吗？`,
      onOk: () => {
        loading.value = true;
        doMarkReportAsTokenBatch(satisfiedRecords);
      },
    });
  }

  function doMarkReportAsTokenBatch(satisfiedRecords) {
    if (signBoardConntect.value) {
      currentSignRows.value = satisfiedRecords;
      signRef.value?.startSign();
    } else {
      createConfirm({
        iconType: 'warning',
        title: '签字板未连接',
        content: '确定不签名领走吗?',
        onOk: () => {
          loading.value = true;
          markAsTakenBatch({ ids: satisfiedRecords.map((record) => record.id) })
            .then((res) => {
              if (res.success) {
                message.success('操作成功！');
                reload();
                selectedRows.value = [];
                selectedRowKeys.value = [];
                currentSignRows.value = [];
              } else {
                message.error('操作失败！');
              }
            })
            .finally(() => {
              loading.value = false;
            });
        },
        onCancel: () => {
          currentSignRows.value = [];
        },
      });
    }
  }

  async function handleBatchResetState() {
    if (selectedRows.value.length == 0) {
      message.error('请先选择一条记录！');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `共选择了${selectedRows.value.length}条记录，确定要重置状态吗？`,
      onOk: () => {
        loading.value = true;
        resetState4ReGeneratePdf({ ids: selectedRows.value.map((record) => record.id) })
          .then((res) => {
            if (res.success) {
              message.success('操作成功！');
              reload();
              selectedRows.value = [];
              selectedRowKeys.value = [];
            } else {
              message.error('操作失败！');
            }
          })
          .finally(() => {
            loading.value = false;
          });
      },
    });
  }

  /**pdf预览*/
  const pdfModalRef = ref<any>();
  function showPdfModal(record) {
    if (!record.id) {
      message.error('请先选择一条记录！');
      return;
    }
    if (!record.ereportUrl) {
      message.error('该记录没有电子报告！');
      return;
    }
    pdfModalRef.value.open(record.ereportUrl, record.name + '电子报告');
  }

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  /**
   * 查询
   */
  async function searchQuery() {
    if (regDateRange.value) {
      queryParam.dateStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
      queryParam.dateEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
    }
    await reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
  }

  function reloadCurrent() {
    if (currentRow.value.id) {
      getRegById({ regId: currentRow.value.id }).then((record) => {
        Object.assign(currentRow.value, record);
      });
    }
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    currentRow.value = record;
    emit('rowClick', record);
  }

  /**
   * 取走
   */
  function handleToked(record: Recordable) {
    if (record.paperReportStatus == '已取走') {
      message.warn('该记录已标记为：已取走，请勿重复操作！');
      return;
    }

    if (!record.paperReportStatus || record.paperReportStatus == '待打印') {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '该记录还未打印，确定要标记为已取走吗？',
        onOk: () => {
          doMarkReportAsTaken(record);
        },
      });
    } else {
      doMarkReportAsTaken(record);
    }
  }

  function reGeneratePdf(record) {
    if (!record.id) {
      message.error('请先选择一条记录！');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确定要重新生成电子报告吗？',
      onOk: () => {
        loading.value = true;
        regenratePdf({ customerRegId: record.id })
          .then((res) => {
            if (res.success) {
              message.success('操作成功！');
              reload();
            } else {
              message.error('操作失败！');
            }
          })
          .finally(() => {
            loading.value = false;
          });
      },
    });
  }

  function sendPdf(record) {
    if (!record.id) {
      message.error('请先选择一条记录！');
      return;
    }
    if (!record.ereportUrl) {
      message.error('该记录尚未生成电子报告！');
      return;
    }

    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: '确定要发送报告通知吗？',
      onOk: () => {
        loading.value = true;
        sendReportNotify({ customerRegId: record.id })
          .then((res) => {
            if (res.success) {
              message.success('操作成功！');
              reload();
            } else {
              message.error('操作失败！');
            }
          })
          .finally(() => {
            loading.value = false;
          });
      },
    });
  }

  function doMarkReportAsTaken(record) {
    if (signBoardConntect.value) {
      currentSignRow.value = record;
      signRef.value?.startSign();
    } else {
      createConfirm({
        iconType: 'warning',
        title: '签字板未连接',
        content: '确定不签名领走吗?',
        onOk: () => {
          markReportAsTaken({ id: record.id }).then((res) => {
            if (res.success) {
              message.success('操作成功！');
              currentSignRow.value = {};
              reload();
            } else {
              message.error('操作失败！');
            }
          });
        },
        onCancel: () => {
          currentSignRow.value = {};
        },
      });
    }
  }

  /**
   * 操作记录
   */
  function handleActionRecord(record) {
    if (!record.id) {
      message.error('请先选择一条记录！');
      return;
    }
    actionRecordList.value.open(record.id);
  }

  /**
   * 打印
   */
  async function handlePrint(record) {
    loading.value = true;
    if (!record.id) {
      message.error('请先选择一条记录！');
      return;
    }
    if (record.ereportUrl) {
      await handlePrintFromUrl(record);
    } else {
      let templateRes = await getMatchedTemplateId(record);
      let report = await getReport(templateRes, record);
      let pdfData = await exportPdf(report, record);
      await printPDFBlob(pdfData, 'report');
    }

    updateReportPrintStatusByIds({ id: record.id })
      .then((res) => {
        if (res.success) {
          message.success('操作成功！');
        } else {
          message.error('操作失败！');
        }
      })
      .finally(() => {
        loading.value = false;
      });
  }

  async function handlePrintFromUrl(record) {
    if (!record.ereportUrl) {
      message.error('该记录没有电子报告！');
      return;
    }
    message.loading('正在打印，请稍后...', 10);
    printRemoteFile(record.ereportUrl);
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    let action = [
      {
        label: '审阅',
        onClick: handleEdit.bind(null, record),
      },
      /*   {
        label: '取走',
        onClick: handleToked.bind(null, record),
      },*/
    ];
    return action;
  }
  function getMatchedTemplateId(reg) {
    if (!reg.id) {
      return null;
    }
    let examCategory = reg.examCategory;
    let regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == examCategory);
    return regTemplateList[0]?.id || null;
  }
  function fetchReportTemplateList() {
    listByType({ type: '报告', regType: '个人' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
      }
    });
  }
  function fetchGeneratePdfBackendsFlag() {
    querySysParamByCode({ code: 'generatePdfBackends' }).then((res) => {
      generatePdfBackendsFlag.value = res.result;
    });
  }

  onMounted(() => {
    fetchReportTemplateList();
    fetchGeneratePdfBackendsFlag();
  });

  onMounted(() => {
    const savedSortOrder = localStorage.getItem('summaryRegDateType');
    queryParam.dateType = savedSortOrder || '降序';
  });

  watch(
    () => queryParam.dateType,
    (newSortOrder) => {
      localStorage.setItem('summaryRegDateType', newSortOrder);
    }
  );

  defineExpose({
    searchQuery,
    reloadPage,
    reloadCurrent,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 8px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }
</style>
