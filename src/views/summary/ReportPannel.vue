<template>
  <div style="padding: 5px">
    <a-row :gutter="4">
      <a-col :span="8">
        <a-card size="small" title="体检登记列表" style="padding: 0; min-height: 90vh"
          >
          <customer-reg-list4-print ref="customerRegList" @row-click="handleRegTableRowClick" />
        </a-card>
      </a-col>
      <a-col :span="16">
        <customer-reg-report-pannel ref="customerRegReportPannel" @success="reloadReg" />
      </a-col>
    </a-row>
  </div>
</template>
<script lang="ts" setup name="ReportPannel">
  import { ref } from 'vue';
  import { ICustomerReg } from '#/types';
  import CustomerRegList4Print from '@/views/summary/CustomerRegList4Print.vue';
  import CustomerRegReportPannel from '@/views/summary/CustomerRegReportPannel.vue';

  const customerRegReportPannel = ref(null);

  /**体检人员列表部分*/
  const customerRegList = ref(null);
  const currentReg = ref<ICustomerReg>(null);
  function reloadReg() {
    customerRegList.value?.reloadCurrent();
  }

  async function handleRegTableRowClick(selectedRow) {
    currentReg.value = selectedRow;
    if (selectedRow.id) {
      customerRegReportPannel.value?.open(currentReg.value);
    }
  }
</script>
<style scoped></style>
