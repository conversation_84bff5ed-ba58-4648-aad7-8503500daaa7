<template>
  <div class="p-2">
    <!--引用表格 + 右侧面板布局-->
    <a-row :gutter="6">
      <a-col :span="8">
        <a-typography-title :level="5" style="margin: 4px 0 8px">职业检团体报告</a-typography-title>
        <!--查询区域（左侧）-->
        <div class="jeecg-basic-table-form-container compact-form">
          <a-form
            ref="formRef"
            @keyup.enter="searchQuery"
            :model="queryParam"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :labelAlign="'left'"
            :colon="false"
            :size="'small'"
          >
            <a-row :gutter="8">
              <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                <a-form-item name="companyId">
                  <template #label><span title="所在单位">所在单位</span></template>
                  <j-async-search-select placeholder="请选择所在单位" v-model:value="queryParam.companyId" dict="company where del_flag=0,name,id" />
                </a-form-item>
              </a-col>
              <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                <a-form-item name="regName">
                  <template #label><span title="预约名称">预约名称</span></template>
                  <a-input placeholder="请输入预约名称" v-model:value="queryParam.regName" />
                </a-form-item>
              </a-col>
              <template v-if="toggleSearchStatus">
                <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                  <a-form-item name="examType">
                    <template #label><span title="体检类别">体检类别</span></template>
                    <j-dict-select-tag placeholder="请选择体检类别" v-model:value="queryParam.examType" dict-code="examination_type" />
                  </a-form-item>
                </a-col>
                <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                  <a-form-item>
                    <template #label><span title="开始日期">开始日期</span></template>
                    <div style="display: flex">
                      <a-form-item name="startCheckDate_begin" style="margin-bottom: 0">
                        <a-date-picker
                          value-format="YYYY-MM-DD"
                          placeholder="请选择开始日期"
                          v-model:value="queryParam.startCheckDate_begin"
                          class="query-group-cust"
                        />
                      </a-form-item>
                      <span class="query-group-split-cust">~</span>
                      <a-form-item name="startCheckDate_end" style="margin-bottom: 0">
                        <a-date-picker
                          value-format="YYYY-MM-DD"
                          placeholder="请选择结束日期"
                          v-model:value="queryParam.startCheckDate_end"
                          class="query-group-cust"
                        />
                      </a-form-item>
                    </div>
                  </a-form-item>
                </a-col>
                <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                  <a-form-item name="serviceManager">
                    <template #label><span title="客服专员">客服专员</span></template>
                    <j-select-user-by-dept
                      placeholder="请选择客服专员"
                      v-model:value="queryParam.serviceManager"
                      @change="(value) => handleFormJoinChange('serviceManager', value)"
                    />
                  </a-form-item>
                </a-col>
                <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                  <a-form-item name="checkoutStatus">
                    <template #label><span title="封账状态">封账状态</span></template>
                    <j-switch placeholder="请选择封账状态" v-model:value="queryParam.checkoutStatus" :options="[1, 0]" query />
                  </a-form-item>
                </a-col>
                <a-col :xl="24" :lg="24" :md="24" :sm="24" :xs="24">
                  <a-form-item name="lockStatus">
                    <template #label><span title="锁定状态">锁定状态</span></template>
                    <j-switch placeholder="请选择锁定状态" v-model:value="queryParam.lockStatus" :options="[1, 0]" query />
                  </a-form-item>
                </a-col>
              </template>
              <a-col :xl="24" :lg="24" :md="24" :sm="24">
                <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
                  <a-col>
                    <a-button size="small" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                    <a-button size="small" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                    <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                      {{ toggleSearchStatus ? '收起' : '展开' }}
                      <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                    </a>
                  </a-col>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
        <BasicTable @register="registerTable" :rowSelection="null" :size="'small'" :striped="true" :columns="listColumns">
          <!--操作栏-->
          <template #action="{ record }">
            <TableAction :actions="getTableAction(record)" />
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'summary'">
              <div class="row-wrap">
                <div class="row-primary">
                  <span class="title" :title="record.regName">{{ record.regName }}</span>
                  <a-tag :color="record.checkoutStatus==='1' ? 'green' : 'red'" class="ml-2">
                    {{ record.checkoutStatus==='1' ? '已封账' : '未封账' }}
                  </a-tag>
                  <a-tag color="blue" class="ml-2">{{ (record.personCount || 0) + '人' }}</a-tag>
                  <span class="date-range ml-2">
                    {{ (record.startCheckDate || '').slice(0,10) }} - {{ (record.endCheckDate || '').slice(0,10) }}
                  </span>
                </div>
                <div class="row-secondary">
                  <span class="company" :title="record.companyName">{{ record.companyName }}</span>
                  <span v-if="record.examType" class="sep">·</span>
                  <span class="muted">{{ record.examType }}</span>
                </div>
              </div>
            </template>
          </template>
        </BasicTable>
      </a-col>
      <a-col :span="16">
        <ZyCompanyRegReportPannel ref="zyReportPannelRef" />
      </a-col>
    </a-row>
    <!-- 表单区域 -->
    <ZyCompanyRegReportModal ref="reportModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="CompanyRegList4Report" setup>
  import { provide, reactive, ref } from 'vue';
  import { companyRegIdKey, companyRegKey } from '/@/providekey/provideKeys';
  import { BasicTable, TableAction } from '/@/components/Table';
  import type { BasicColumn } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from '@/views/reg/CompanyReg.data';
  import { batchDelete, deleteOne, list } from '@/views/reg/CompanyReg.api';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSwitch from '/@/components/Form/src/jeecg/components/JSwitch.vue';
  import JSelectUserByDept from '/@/components/Form/src/jeecg/components/JSelectUserByDept.vue';
  import { JAsyncSearchSelect } from '@/components/Form';
  import ZyCompanyRegReportPannel from '@/views/summary/components/ZyCompanyRegReportPannel.vue';

  const companyRegId = reactive({ value: '', setValue: (val) => (companyRegId.value = val) });
  const companyReg = reactive({ value: {}, setValue: (val) => (companyReg.value = val) });
  provide(companyRegIdKey, companyRegId);
  provide(companyRegKey, companyReg);

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const listColumns: BasicColumn[] = [
    {
      title: '团体登记',
      dataIndex: 'summary',
      key: 'summary',
      align: 'left',
      width: 700,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 88,
      fixed: 'right',
      slots: { customRender: 'action' },
    },
  ];
  const zyReportPannelRef = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      api: list,
      columns: listColumns,
      canResize: false,
      showTableSetting: false,
      useSearchForm: false,
      canColDrag: true,
      size: 'small',
      clickToRowSelect: false,
      actionColumn: {
        width: 88,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        queryParam.examType='职业病体检'
        return Object.assign(params, queryParam);
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    zyReportPannelRef.value?.open(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '查看',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }

  /**
   * form点击事件(以逗号分割)
   * @param key
   * @param value
   */
  function handleFormJoinChange(key, value) {
    queryParam[key] = value.join(',');
  }
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 12px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
