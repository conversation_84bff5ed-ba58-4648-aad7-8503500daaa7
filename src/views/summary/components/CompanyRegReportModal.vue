<template>
  <a-modal :open="visible" @ok="handleCancel" @cancel="handleCancel" :footer="null" width="100%" cancelText="关闭" wrap-class-name="full-modal">
    <template #title>
      <a-space>
        <a-typography-title :level="5">{{ title }}</a-typography-title>
        <a-select
          @change="previewReportByTemplate"
          placeholder="请选择报告模版"
          v-model:value="currentReportTemplateId"
          size="small"
          style="width: 150px; margin-left: 20px"
          :options="reportTemplateList"
        />
        <a-select
          placeholder="请选择科室"
          @change="changeDept"
          v-model:value="currentCompanyDeptId"
          allow-clear
          size="small"
          style="width: 150px; margin-left: 20px"
          :options="companyDeptList"
        />
      </a-space>
    </template>
    <a-spin :spinning="summaryAdviceLoading">
      <div class="viewer-host">
        <div ref="viewerEl" class="viewer-el"></div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { defineExpose, nextTick, reactive, ref, onMounted } from 'vue';
  import { message, SelectProps } from 'ant-design-vue';
  import { getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { getTeamReport } from '@/views/summary/Summary.api';
  import {getCompanyDeptListByPid} from "@/views/reg/CompanyReg.api";
  import { ensureArjsFonts } from '@/utils/arjs';
  //import '@grapecity/activereports/styles/ar-js-designer.css';

  const title = ref<string>('');
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const viewerEl = ref<HTMLElement | null>(null);
  let viewer: any;
  onMounted(async () => {
    await nextTick();
    await ensureArjsFonts();
    const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
    if (!ARJS || !viewerEl.value) return;
    viewer = new ARJS.ReportViewer.Viewer(viewerEl.value, {
      language: 'zh',
      viewMode: 'Continuous',
      ErrorHandler: (error) => {
        console.error(error?.message || error);
        return true;
      },
    });
    viewer.viewMode = 'Continuous';
  });

  const summaryAdviceLoading = ref<boolean>(false);
  const reportDataLoading = ref<boolean>(false);
  const currentCompanyReg = ref<any>({});
  /**报告预览打印*/
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const originCompanyDeptList = ref([]);
  const companyDeptList = ref([]);
  const currentReportTemplateId = ref(null);
  const currentCompanyDeptId = ref(null);
  const emit = defineEmits(['register', 'success']);
  const labelCol = reactive({
    xs: 24,
    sm: 4,
    xl: 6,
    xxl: 4,
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  async function open(reg) {
    visible.value = true;
    currentCompanyReg.value = reg;
    title.value = `单位体检报告-${reg.regName}`;

    let companyDeptRes = await getCompanyDeptListByPid({
      pid: currentCompanyReg.value.companyId,
      pageSize: 10000
    });
    if (companyDeptRes?.length > 0) {
      originCompanyDeptList.value = companyDeptRes;
      currentCompanyDeptId.value = companyDeptRes[0].id;
      companyDeptList.value = companyDeptRes.map((item) => {
        return {
          value: item.id,
          label: item.name,
        };
      });

    }

    let reportTemplateRes = await getReportTemplate({type: '报告', regType: '团体'});
    if (reportTemplateRes.success) {
      originReportTemplateList.value = reportTemplateRes.result;
      if (reportTemplateRes.result?.length == 0) {
        message.error('没有可用的团体报告模版！');
        return;
      }
      currentReportTemplateId.value = reportTemplateRes.result[0].id;
      reportTemplateList.value = reportTemplateRes.result.map((item) => {
        return {
          value: item.id,
          label: item.name,
        };
      });
      previewReport(currentReportTemplateId.value,currentCompanyDeptId.value);
    } else {
      message.error('获取团体报告模版失败！');
    }

  }
  /**
   * 取消按钮回调事件
   */
  function handleCancel() {
    visible.value = false;
  }

  async function previewReport(templateId,deptId) {
    if (!currentCompanyReg.value.id) {
      message.warn('请选择单位！');
      return;
    }
    if (!currentReportTemplateId.value) {
      message.warn('请选择报告模板！');
      return;
    }
    //获取报告模版内容
    try {
      summaryAdviceLoading.value = true;
      const templateRes = await getTemplateById({ id: templateId });
      let template = JSON.parse(templateRes.content);
      const reportDataRes = await getTeamReport({ companyRegId: currentCompanyReg.value.id,companyDeptId: deptId });
      summaryAdviceLoading.value = false;
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);

        const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
        if (!ARJS) return;
        await ensureArjsFonts();
        await nextTick();
        const host = viewerEl.value as HTMLElement;
        if (!host) return;
        if (!viewer) {
          viewer = new ARJS.ReportViewer.Viewer(host, {
            language: 'zh',
            viewMode: 'Continuous',
            ErrorHandler: (error) => {
              console.error(error?.message || error);
              return true;
            },
          });
          viewer.viewMode = 'Continuous';
        }
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(template);
        viewer.viewMode = 'Continuous';
      } else {
        message.error('获取报告数据失败!');
      }
    } catch (error) {
      console.error(error);
    }
  }

  function previewReportByTemplate() {
    previewReport(currentReportTemplateId.value,currentCompanyDeptId.value);
  }
  function changeDept() {
    previewReport(currentReportTemplateId.value,currentCompanyDeptId.value);
  }

  defineExpose({
    open,
    disableSubmit,
  });
</script>

<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 200px);
  }
  .viewer-el {
    width: 100%;
    height: 100%;
  }
</style>
<style>
  #reportViewer {
    margin: 0 auto;
    width: 100%;
    height: 100vh;
  }
</style>
