<template>
  <a-card :title="title">
    <template #extra>
      <a-space>
        <a-select
          placeholder="请选择报告模版"
          v-model:value="currentReportTemplateId"
          size="small"
          style="width: 180px"
          :options="reportTemplateList"
          @change="previewReportByTemplate"
        />
        <a-select
          placeholder="请选择科室"
          v-model:value="currentCompanyDeptId"
          allow-clear
          size="small"
          style="width: 160px"
          :options="companyDeptList"
          @change="changeDept"
        />
      </a-space>
    </template>
    <a-spin :spinning="summaryAdviceLoading">
      <div class="viewer-host">
        <div ref="viewerEl" class="viewer-el"></div>
      </div>
    </a-spin>
  </a-card>
  
</template>

<script lang="ts" setup>
  import { defineExpose, nextTick, reactive, ref } from 'vue';
  import { message, SelectProps } from 'ant-design-vue';
  import { getReportTemplate, getTemplateById } from '@/views/basicinfo/Template.api';
  import { getTeamReport } from '@/views/summary/Summary.api';
  import { getCompanyDeptListByPid } from '@/views/reg/CompanyReg.api';
  import { ensureArjsFonts } from '@/utils/arjs';

  const title = ref<string>('团体体检报告');
  const viewerEl = ref<HTMLElement | null>(null);
  let viewer: any;

  const summaryAdviceLoading = ref<boolean>(false);
  const currentCompanyReg = ref<any>({});
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const originCompanyDeptList = ref([]);
  const companyDeptList = ref([]);
  const currentReportTemplateId = ref<any>(null);
  const currentCompanyDeptId = ref<any>(null);

  async function open(reg) {
    currentCompanyReg.value = reg;
    title.value = `团体体检报告 - ${reg.regName}`;
    await preloadDeptsAndTemplates();
    if (currentReportTemplateId.value) {
      await previewReport(currentReportTemplateId.value, currentCompanyDeptId.value);
    } else {
      message.error('没有可用的团体报告模版！');
    }
  }

  async function preloadDeptsAndTemplates() {
    // 科室
    const companyDeptRes = await getCompanyDeptListByPid({
      pid: currentCompanyReg.value.companyId,
      pageSize: 10000,
    });
    if (companyDeptRes?.length > 0) {
      originCompanyDeptList.value = companyDeptRes;
      currentCompanyDeptId.value = companyDeptRes[0].id;
      companyDeptList.value = companyDeptRes.map((item) => ({ value: item.id, label: item.name }));
    }
    // 模板
    const reportTemplateRes = await getReportTemplate({ type: '报告', regType: '团体' });
    if (reportTemplateRes?.success) {
      originReportTemplateList.value = reportTemplateRes.result;
      if (reportTemplateRes.result?.length > 0) {
        currentReportTemplateId.value = reportTemplateRes.result[0].id;
        reportTemplateList.value = reportTemplateRes.result.map((item) => ({ value: item.id, label: item.name }));
      }
    }
  }

  async function previewReport(templateId, deptId) {
    if (!currentCompanyReg.value?.id) {
      message.warn('请选择单位！');
      return;
    }
    if (!templateId) {
      message.warn('请选择报告模板！');
      return;
    }
    try {
      summaryAdviceLoading.value = true;
      const templateRes = await getTemplateById({ id: templateId });
      const template = JSON.parse(templateRes.content);
      const reportDataRes = await getTeamReport({ companyRegId: currentCompanyReg.value.id, companyDeptId: deptId });
      summaryAdviceLoading.value = false;
      if (reportDataRes.success) {
        const reportData = reportDataRes.result;
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);

        const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
        if (!ARJS) return;
        await ensureArjsFonts();
        await nextTick();
        const host = viewerEl.value as HTMLElement;
        if (!host) return;
        if (!viewer) {
          viewer = new ARJS.ReportViewer.Viewer(host, {
            language: 'zh',
            viewMode: 'Continuous',
            ErrorHandler: (error) => {
              console.error(error?.message || error);
              return true;
            },
          });
          viewer.viewMode = 'Continuous';
        }
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(template);
        viewer.viewMode = 'Continuous';
      } else {
        message.error('获取团体报告数据失败!');
      }
    } catch (e) {
      console.error(e);
    }
  }

  function previewReportByTemplate() {
    previewReport(currentReportTemplateId.value, currentCompanyDeptId.value);
  }
  function changeDept() {
    previewReport(currentReportTemplateId.value, currentCompanyDeptId.value);
  }

  defineExpose({ open });
</script>

<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 200px);
  }
  .viewer-el {
    width: 100%;
    height: 100%;
  }
</style>


